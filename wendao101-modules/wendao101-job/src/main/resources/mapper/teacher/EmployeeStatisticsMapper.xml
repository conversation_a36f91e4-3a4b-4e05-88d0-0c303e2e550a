<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.EmployeeStatisticsMapper">
    <resultMap type="EmployeeStatistics" id="EmployeeStatisticsResult">
        <result property="id"    column="id"    />
        <result property="dealAmount"    column="deal_amount"    />
        <result property="withdrawnAmount"    column="withdrawn_amount"    />
        <result property="moneyInTransit"    column="money_in_transit"    />
        <result property="withdrawableAmount"    column="withdrawable_amount"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="timeQueryStr"    column="time_query_str"    />
        <result property="withdrawnAmountFee"    column="withdrawn_amount_fee"    />
        <result property="notWithdrawnFee"    column="not_withdrawn_fee"    />
        <result property="totalWithdrawnFee"    column="total_withdrawn_fee"    />
        <result property="withdrawnNetCommission"    column="withdrawn_net_commission"    />
        <result property="notWithdrawnNetCommission"    column="not_withdrawn_net_commission"    />
        <result property="totalWithdrawnNetCommission"    column="total_withdrawn_net_commission"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="leaderId"    column="leader_id"    />
        <result property="leaderNickName"    column="leader_nick_name"    />
        <result property="parentLeaderId"    column="parent_leader_id"    />
        <result property="parentLeaderNickName"    column="parent_leader_nick_name"    />
        <result property="orderNum"    column="order_num"    />
    </resultMap>

    <sql id="selectEmployeeStatisticsVo">
        SELECT
            id,
            deal_amount,
            withdrawn_amount,
            money_in_transit,
            withdrawable_amount,
            create_time,
            update_time,
            time_query_str,
            withdrawn_amount_fee,
            not_withdrawn_fee,
            total_withdrawn_fee,
            withdrawn_net_commission,
            not_withdrawn_net_commission,
            total_withdrawn_net_commission,
            employee_id,
            nick_name,
            leader_id,
            leader_nick_name,
            parent_leader_id,
            parent_leader_nick_name,
            order_num
        FROM
            `wendao101-order`.employee_statistics
    </sql>

    <select id="selectEmployeeStatisticsList" parameterType="EmployeeStatistics" resultMap="EmployeeStatisticsResult">
        <include refid="selectEmployeeStatisticsVo"/>
        <where>  
            <if test="timeQueryStr != null  and timeQueryStr != '' "> and time_query_str = #{timeQueryStr}</if>
            <if test="employee_id != null "> and employee_id = #{employeeId}</if>
            <if test="nickName != null and nickName != '' "> and nick_name = #{nickName}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectEmployeeStatisticsById" parameterType="Long" resultMap="EmployeeStatisticsResult">
        <include refid="selectEmployeeStatisticsVo"/>
        where id = #{id}
    </select>
    <select id="selectEmployeeStatisticsListGroupByEmpId" resultMap="EmployeeStatisticsResult">
        select sum(deal_amount) as deal_amount,
               sum(withdrawn_amount) as withdrawn_amount,
               sum(money_in_transit) as money_in_transit,
               sum(withdrawable_amount) as withdrawable_amount,
               sum(withdrawn_amount_fee) as withdrawn_amount_fee,
               sum(not_withdrawn_fee) as not_withdrawn_fee,
               sum(total_withdrawn_fee) as total_withdrawn_fee,
               sum(withdrawn_net_commission) as withdrawn_net_commission,
               sum(not_withdrawn_net_commission) as not_withdrawn_net_commission,
               sum(total_withdrawn_net_commission) as total_withdrawn_net_commission,
               sum(order_num) as order_num,
               employee_id,
               nick_name,
               leader_id,
               leader_nick_name,
               parent_leader_id,
               parent_leader_nick_name
        from `wendao101-order`.employee_teacher_statistics where time_query_str = #{timeRangeStr} GROUP BY employee_id

    </select>
    <select id="selectEmployeeStatisticsListGroupByEmpIdTimeNull" resultMap="EmployeeStatisticsResult">
        select sum(deal_amount) as deal_amount,
               sum(withdrawn_amount) as withdrawn_amount,
               sum(money_in_transit) as money_in_transit,
               sum(withdrawable_amount) as withdrawable_amount,
               sum(withdrawn_amount_fee) as withdrawn_amount_fee,
               sum(not_withdrawn_fee) as not_withdrawn_fee,
               sum(total_withdrawn_fee) as total_withdrawn_fee,
               sum(withdrawn_net_commission) as withdrawn_net_commission,
               sum(not_withdrawn_net_commission) as not_withdrawn_net_commission,
               sum(total_withdrawn_net_commission) as total_withdrawn_net_commission,
               sum(order_num) as order_num,
               employee_id,
               nick_name,
               leader_id,
               leader_nick_name,
               parent_leader_id,
               parent_leader_nick_name
        from `wendao101-order`.employee_teacher_statistics where time_query_str is null GROUP BY employee_id
    </select>
    <select id="selectEmployeeSaleStatisticsListGroupByEmpId"
            resultType="com.wendao101.teacher.domain.EmployeeSalesStatisticsDays">
        select sum(deal_amount) as totalSales,
               employee_id as userId,
               nick_name as nickName,
               leader_id as leaderId,
               leader_nick_name as leaderNickName,
               parent_leader_id as parentLeaderId,
               parent_leader_nick_name as parentLeaderNickName
        from `wendao101-order`.employee_teacher_statistics where time_query_str = #{timeQueryStr} GROUP BY employee_id
    </select>
    <select id="selectEmployeeSaleStatisticsListGroupByEmpIdForDay"
            resultType="com.wendao101.teacher.domain.EmployeeSalesStatistics">
        select sum(deal_amount) as totalSales,
               sum(order_num) as totalOrders,
               COALESCE(SUM(deal_amount) / NULLIF(SUM(order_num), 0), 0) as avgOrderAmount,
               employee_id as userId,
               nick_name as nickName,
               leader_id as leaderId,
               leader_nick_name as leaderNickName,
               parent_leader_id as parentLeaderId,
               parent_leader_nick_name as parentLeaderNickName
        from `wendao101-order`.employee_teacher_statistics where time_query_str = #{timeQueryStr} GROUP BY employee_id
    </select>
    <insert id="insertEmployeeStatistics" parameterType="EmployeeStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.employee_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dealAmount != null">deal_amount,</if>
            <if test="withdrawnAmount != null">withdrawn_amount,</if>
            <if test="moneyInTransit != null">money_in_transit,</if>
            <if test="withdrawableAmount != null">withdrawable_amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="timeQueryStr != null">time_query_str,</if>
            <if test="withdrawnAmountFee != null">withdrawn_amount_fee,</if>
            <if test="notWithdrawnFee != null">not_withdrawn_fee,</if>
            <if test="totalWithdrawnFee != null">total_withdrawn_fee,</if>
            <if test="withdrawnNetCommission != null">withdrawn_net_commission,</if>
            <if test="notWithdrawnNetCommission != null">not_withdrawn_net_commission,</if>
            <if test="totalWithdrawnNetCommission != null">total_withdrawn_net_commission,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="leaderId != null">leader_id,</if>
            <if test="leaderNickName != null">leader_nick_name,</if>
            <if test="parentLeaderId != null">parent_leader_id,</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name,</if>
            <if test="orderNum != null">order_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dealAmount != null">#{dealAmount},</if>
            <if test="withdrawnAmount != null">#{withdrawnAmount},</if>
            <if test="moneyInTransit != null">#{moneyInTransit},</if>
            <if test="withdrawableAmount != null">#{withdrawableAmount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="timeQueryStr != null">#{timeQueryStr},</if>
            <if test="withdrawnAmountFee != null">#{withdrawnAmountFee},</if>
            <if test="notWithdrawnFee != null">#{notWithdrawnFee},</if>
            <if test="totalWithdrawnFee != null">#{totalWithdrawnFee},</if>
            <if test="withdrawnNetCommission != null">#{withdrawnNetCommission},</if>
            <if test="notWithdrawnNetCommission != null">#{notWithdrawnNetCommission},</if>
            <if test="totalWithdrawnNetCommission != null">#{totalWithdrawnNetCommission},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="leaderId != null">#{leaderId},</if>
            <if test="leaderNickName != null">#{leaderNickName},</if>
            <if test="parentLeaderId != null">#{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">#{parentLeaderNickName},</if>
            <if test="orderNum != null">#{orderNum},</if>
         </trim>
    </insert>

    <update id="updateEmployeeStatistics" parameterType="EmployeeStatistics">
        update `wendao101-order`.employee_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="dealAmount != null">deal_amount = #{dealAmount},</if>
            <if test="withdrawnAmount != null">withdrawn_amount = #{withdrawnAmount},</if>
            <if test="moneyInTransit != null">money_in_transit = #{moneyInTransit},</if>
            <if test="withdrawableAmount != null">withdrawable_amount = #{withdrawableAmount},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="timeQueryStr != null">time_query_str = #{timeQueryStr},</if>
            <if test="withdrawnAmountFee != null">withdrawn_amount_fee = #{withdrawnAmountFee},</if>
            <if test="notWithdrawnFee != null">not_withdrawn_fee = #{notWithdrawnFee},</if>
            <if test="totalWithdrawnFee != null">total_withdrawn_fee = #{totalWithdrawnFee},</if>
            <if test="withdrawnNetCommission != null">withdrawn_net_commission = #{withdrawnNetCommission},</if>
            <if test="notWithdrawnNetCommission != null">not_withdrawn_net_commission = #{notWithdrawnNetCommission},</if>
            <if test="totalWithdrawnNetCommission != null">total_withdrawn_net_commission = #{totalWithdrawnNetCommission},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="leaderId != null">leader_id = #{leaderId},</if>
            <if test="leaderNickName != null">leader_nick_name = #{leaderNickName},</if>
            <if test="parentLeaderId != null">parent_leader_id = #{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name = #{parentLeaderNickName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeStatisticsById" parameterType="Long">
        delete from `wendao101-order`.employee_statistics where id = #{id}
    </delete>

    <delete id="deleteEmployeeStatisticsByIds" parameterType="String">
        delete from `wendao101-order`.employee_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteEmployeeStatisticsBigerThanId">
        delete from `wendao101-order`.employee_statistics where id > #{id}
        <if test="timeRange != null  and timeRange != ''"> and time_query_str = #{timeRange}</if>
    </delete>
    <delete id="deleteEmployeeStatisticsBigerThanIdAndQueryStringIsNull">
        delete from `wendao101-order`.employee_statistics where id > #{id} and time_query_str is null
    </delete>
</mapper> 