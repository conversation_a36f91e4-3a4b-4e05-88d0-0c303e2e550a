package com.wendao101.order.controller;

import com.alibaba.fastjson2.JSON;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.region.Region;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.common.core.utils.poi.ExcelUtil;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.core.web.page.TableDataInfoWithDrawRecord;
import com.wendao101.common.core.web.page.TableDataInfoWithEmployeeDataSum;
import com.wendao101.common.core.web.page.TableDataInfoWithSumSaleMoney;
import com.wendao101.common.dto.TeacherStatisticsTaskDTO;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.order.domain.*;
import com.wendao101.order.service.IDerivedRecordService;
import com.wendao101.order.service.ITeacherStatisticsService;
import com.wendao101.order.service.ITeacherStatisticsTimeQueryService;
import com.wendao101.order.service.IWithdrawRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 教师统计Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/teacher/saleStatistics")
public class TeacherStatisticsController extends BaseController {

    @Value("${wendao.video.secretId}")
    private String secretId;
    @Value("${wendao.video.secretKey}")
    private String secretKey;
    @Value("${wendao.cos.region}")
    private String region;
    @Value("${wendao.cos.outputBucket}")
    private String outputBucket;

    @Autowired
    private ITeacherStatisticsService teacherStatisticsService;

    @Autowired
    private IWithdrawRecordService withdrawRecordService;
    @Autowired
    private RedisService redisService;

    @Resource
    private IDerivedRecordService derivedRecordService;
    @Autowired
    private ITeacherStatisticsTimeQueryService teacherStatisticsTimeQueryService;

    public static final String EMPLOYEE_TEACHER_STATISTICS_TIME_QUERY = "employeeTeacherStatisticsTimeQuery";

    @GetMapping("/queryTaskList")
    public TableDataInfo queryTaskList() {
        Long teacherId = SecurityUtils.getUserId();
        if (teacherId != 3L) {
            return TableDataInfo.error("没有权限");
        }
        startPage();
        List<TeacherStatisticsTimeQuery> teacherStatisticsTimeQueries = teacherStatisticsTimeQueryService.selectTeacherStatisticsTimeQueryList(null);
        return getDataTable(teacherStatisticsTimeQueries);
    }

    @GetMapping("/queryEmployeeTaskList")
    public TableDataInfo queryEmployeeTaskList() {
        Long teacherId = SecurityUtils.getUserId();
        if (teacherId != 3L) {
            return TableDataInfo.error("没有权限");
        }
        startPage();
        List<TeacherStatisticsTimeQuery> teacherStatisticsTimeQueries = teacherStatisticsTimeQueryService.selectEmployeeStatisticsTimeQueryList(null);
        return getDataTable(teacherStatisticsTimeQueries);
    }

    @GetMapping("/employeeList")
    public TableDataInfoWithEmployeeDataSum employeeList(TeacherStatistics teacherStatistics) {
        Long teacherId = SecurityUtils.getUserId();
        if (teacherId != 3L) {
            return TableDataInfoWithEmployeeDataSum.error("没有权限");
        }
        String timeQueryStr = null;
        //如果有时间
        if (teacherStatistics.getTimeQueryId() == null) {
            Date beginTime = teacherStatistics.getBeginTime();
            Date endTime = teacherStatistics.getEndTime();
            if (beginTime != null || endTime != null) {
                //查询最新任务执行begin
                TeacherStatisticsTimeQuery taskExistsNotAllow = teacherStatisticsTimeQueryService.selectNewEmployeeStatisticsTimeQuery();
                if (taskExistsNotAllow != null) {
                    TableDataInfoWithEmployeeDataSum taskExists = new TableDataInfoWithEmployeeDataSum();
                    taskExists.setCode(901);
                    taskExists.setMsg("已有任务等待执行,无法提交新任务");
                    return taskExists;
                }
                com.wendao101.teacher.domain.TeacherStatisticsTimeQuery t = redisService.getCacheObject(EMPLOYEE_TEACHER_STATISTICS_TIME_QUERY);
                if (t != null) {
                    TableDataInfoWithEmployeeDataSum taskExists = new TableDataInfoWithEmployeeDataSum();
                    taskExists.setCode(901);
                    taskExists.setMsg("任务正在执行中,无法提交新任务");
                    return taskExists;
                }

                TeacherStatisticsTaskDTO queryDTO = new TeacherStatisticsTaskDTO();
                queryDTO.setBeginTime(beginTime);
                queryDTO.setEndTime(endTime);
                queryDTO.setAppNameType(teacherStatistics.getAppNameType());
                queryDTO.setTeacherInfo(teacherStatistics.getTeacherInfo());
                String jsonString = JSON.toJSONString(queryDTO);
                TeacherStatisticsTimeQuery teacherStatisticsTimeQuery = new TeacherStatisticsTimeQuery();
                teacherStatisticsTimeQuery.setBeginTime(beginTime);
                teacherStatisticsTimeQuery.setEndTime(endTime);
                teacherStatisticsTimeQuery.setTaskStatus(0);
                teacherStatisticsTimeQuery.setQueryString(jsonString);
                int row = teacherStatisticsTimeQueryService.insertEmployeeStatisticsTimeQuery(teacherStatisticsTimeQuery);
                TableDataInfoWithEmployeeDataSum taskResult = new TableDataInfoWithEmployeeDataSum();
                taskResult.setCode(701);
                taskResult.setMsg("任务已提交,请等会来查看任务执行状态");
                return taskResult;
            }
        } else {
            //-1,-2,-30
            //String query
            if(teacherStatistics.getTimeQueryId()==-1L){
                timeQueryStr = get1QueryString();
            }
            if(teacherStatistics.getTimeQueryId()==-2L){
                timeQueryStr = get2QueryString();
            }
            if(teacherStatistics.getTimeQueryId()==-30L){
                timeQueryStr = get30QueryString();
            }
            if(StringUtils.isBlank(timeQueryStr)){
                TeacherStatisticsTimeQuery tq = teacherStatisticsTimeQueryService.selectEmployeeStatisticsTimeQueryById(teacherStatistics.getTimeQueryId());
                TeacherStatisticsTaskDTO queryDTO = JSON.parseObject(tq.getQueryString(), TeacherStatisticsTaskDTO.class);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if (queryDTO.getBeginTime() != null) {
                    timeQueryStr = sdf.format(tq.getBeginTime()) + "~";
                }
                if (queryDTO.getEndTime() != null) {
                    timeQueryStr = timeQueryStr + sdf.format(tq.getEndTime());
                }
            }
        }
        startPage();
        teacherStatistics.setTimeQueryStr(timeQueryStr);
        List<EmployeeStatistics> list = teacherStatisticsService.selectEmployeeStatisticsList(teacherStatistics);

        TableDataInfoWithEmployeeDataSum result =  getTableDataInfoWithEmployeeDataSum(list);
        //执行合计
        return result;
    }

    private String get30QueryString(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 时间取值 上个月1号0点到本月1号0点
        Calendar calendar = Calendar.getInstance();
        // 本月第一天 00:00:00
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date endDate = calendar.getTime();
        // 上个月第一天 00:00:00
        calendar.add(Calendar.MONTH, -1); // 回退一个月
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设为上月第一天
        Date startDate = calendar.getTime();
        return sdf.format(startDate) + "~" + sdf.format(endDate);
    }

    private String get1QueryString(){
        // 今天的0点和昨天的0点
        // 1. 获取当前时间的Calendar实例
        Calendar calendar = Calendar.getInstance();
        // 2. 获取今天的0点（00:00:00.000）
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date todayZero = calendar.getTime();
        // 3. 获取昨天的0点（日期减1天）
        calendar.add(Calendar.DATE, -1);
        Date yesterdayZero = calendar.getTime();
        // 创建日期格式化器
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(yesterdayZero) + "~" + sdf.format(todayZero);
    }

    private String get2QueryString(){
        // 今天的0点和昨天的0点
        // 1. 获取当前时间的Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 2. 获取今天的0点（00:00:00.000）
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        //Date todayZero = calendar.getTime();

        // 3. 获取昨天的0点（日期减1天）
        calendar.add(Calendar.DATE, -1);
        Date yesterdayZero = calendar.getTime();

        // 4. 获取前天的0点（再减1天）
        calendar.add(Calendar.DATE, -1);  // 在前一天基础上再减1天
        Date dayBeforeYesterdayZero = calendar.getTime();
        // 创建日期格式化器
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(dayBeforeYesterdayZero) + "~" + sdf.format(yesterdayZero);
    }

    /**
     * 查询教师统计列表
     */
    @GetMapping("/list")
    public TableDataInfoWithSumSaleMoney list(TeacherStatistics teacherStatistics) {
        Long teacherId = SecurityUtils.getUserId();
        if (teacherId != 3L) {
            return TableDataInfoWithSumSaleMoney.error("没有权限");
        }
        String timeQueryStr = null;
        //如果有时间
        if (teacherStatistics.getTimeQueryId() == null) {
            Date beginTime = teacherStatistics.getBeginTime();
            Date endTime = teacherStatistics.getEndTime();
            if (beginTime != null || endTime != null) {
                //查询最新任务执行begin
                TeacherStatisticsTimeQuery taskExistsNotAllow = teacherStatisticsTimeQueryService.selectNewTeacherStatisticsTimeQuery();
                if (taskExistsNotAllow != null) {
                    TableDataInfoWithSumSaleMoney taskExists = new TableDataInfoWithSumSaleMoney();
                    taskExists.setCode(901);
                    taskExists.setMsg("已有任务等待执行,无法提交新任务");
                    return taskExists;
                }
                com.wendao101.teacher.domain.TeacherStatisticsTimeQuery t = redisService.getCacheObject("teacherStatisticsTimeQuery");
                if (t != null) {
                    TableDataInfoWithSumSaleMoney taskExists = new TableDataInfoWithSumSaleMoney();
                    taskExists.setCode(901);
                    taskExists.setMsg("任务正在执行中,无法提交新任务");
                    return taskExists;
                }

                TeacherStatisticsTaskDTO queryDTO = new TeacherStatisticsTaskDTO();
                queryDTO.setBeginTime(beginTime);
                queryDTO.setEndTime(endTime);
                queryDTO.setAppNameType(teacherStatistics.getAppNameType());
                queryDTO.setTeacherInfo(teacherStatistics.getTeacherInfo());
                String jsonString = JSON.toJSONString(queryDTO);
                TeacherStatisticsTimeQuery teacherStatisticsTimeQuery = new TeacherStatisticsTimeQuery();
                teacherStatisticsTimeQuery.setBeginTime(beginTime);
                teacherStatisticsTimeQuery.setEndTime(endTime);
                teacherStatisticsTimeQuery.setTaskStatus(0);
                teacherStatisticsTimeQuery.setQueryString(jsonString);
                teacherStatisticsTimeQueryService.insertTeacherStatisticsTimeQuery(teacherStatisticsTimeQuery);
                TableDataInfoWithSumSaleMoney taskResult = new TableDataInfoWithSumSaleMoney();
                taskResult.setCode(701);
                taskResult.setMsg("任务已提交,请等会来查看任务执行状态");
                return taskResult;
            }
        } else {
            TeacherStatisticsTimeQuery tq = teacherStatisticsTimeQueryService.selectTeacherStatisticsTimeQueryById(teacherStatistics.getTimeQueryId());
            TeacherStatisticsTaskDTO queryDTO = JSON.parseObject(tq.getQueryString(), TeacherStatisticsTaskDTO.class);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (queryDTO.getBeginTime() != null) {
                timeQueryStr = sdf.format(tq.getBeginTime()) + "~";
            }
            if (queryDTO.getEndTime() != null) {
                timeQueryStr = timeQueryStr + sdf.format(tq.getEndTime());
            }
            if (queryDTO.getAppNameType() != null) {
                timeQueryStr = timeQueryStr + "~" + queryDTO.getAppNameType();
            }
            if (StringUtils.isNotBlank(queryDTO.getTeacherInfo())) {
                timeQueryStr = timeQueryStr + "~" + queryDTO.getTeacherInfo();
            }
        }
        startPage();
        teacherStatistics.setTimeQueryStr(timeQueryStr);
        List<TeacherStatistics> list = teacherStatisticsService.selectTeacherStatisticsList(teacherStatistics);
        TableDataInfoWithSumSaleMoney tableDataInfoWithSumSaleMoney = getTableDataInfoWithSumSaleMoney(list);
        TableDataInfoWithSumSaleMoney tableDataInfoWithSumSaleMoney1 = teacherStatisticsService.selectTeacherStatisticsSum(teacherStatistics);
        tableDataInfoWithSumSaleMoney.setTotalDealAmount(tableDataInfoWithSumSaleMoney1.getTotalDealAmount());
        tableDataInfoWithSumSaleMoney.setTotalWithdrawnAmount(tableDataInfoWithSumSaleMoney1.getTotalWithdrawnAmount());
        tableDataInfoWithSumSaleMoney.setTotalMoneyInTransit(tableDataInfoWithSumSaleMoney1.getTotalMoneyInTransit());
        tableDataInfoWithSumSaleMoney.setTotalWithdrawableAmount(tableDataInfoWithSumSaleMoney1.getTotalWithdrawableAmount());
        tableDataInfoWithSumSaleMoney.setTotalServiceFee(tableDataInfoWithSumSaleMoney1.getTotalServiceFee());
        if (tableDataInfoWithSumSaleMoney1.getTotalDealAmount() != null && tableDataInfoWithSumSaleMoney1.getTotalWithdrawnAmount().compareTo(BigDecimal.ZERO) > 0) {
            tableDataInfoWithSumSaleMoney.setTotalServiceFeeRate(tableDataInfoWithSumSaleMoney1.getTotalServiceFee().divide(tableDataInfoWithSumSaleMoney1.getTotalWithdrawnAmount(), 2, RoundingMode.HALF_UP));
        } else {
            tableDataInfoWithSumSaleMoney.setTotalServiceFeeRate(BigDecimal.ZERO);
        }
        return tableDataInfoWithSumSaleMoney;
    }

    @PostMapping("/export")
    public AjaxResult export(@RequestBody TeacherStatistics teacherStatistics) {
        Long teacherId = SecurityUtils.getUserId();
        if (teacherId != 3L) {
            return AjaxResult.error("没有权限");
        }
        String timeQueryStr = null;
        if (teacherStatistics.getTimeQueryId() != null) {
            TeacherStatisticsTimeQuery tq = teacherStatisticsTimeQueryService.selectTeacherStatisticsTimeQueryById(teacherStatistics.getTimeQueryId());
            TeacherStatisticsTaskDTO queryDTO = JSON.parseObject(tq.getQueryString(), TeacherStatisticsTaskDTO.class);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (queryDTO.getBeginTime() != null) {
                timeQueryStr = sdf.format(tq.getBeginTime()) + "~";
            }
            if (queryDTO.getEndTime() != null) {
                timeQueryStr = timeQueryStr + sdf.format(tq.getEndTime());
            }
            if (queryDTO.getAppNameType() != null) {
                timeQueryStr = timeQueryStr + "~" + queryDTO.getAppNameType();
            }
            if (StringUtils.isNotBlank(queryDTO.getTeacherInfo())) {
                timeQueryStr = timeQueryStr + "~" + queryDTO.getTeacherInfo();
            }
        }
        teacherStatistics.setTimeQueryStr(timeQueryStr);
        List<TeacherStatistics> list = teacherStatisticsService.selectTeacherStatisticsList(teacherStatistics);
        TableDataInfoWithSumSaleMoney tableDataInfoWithSumSaleMoney1 = teacherStatisticsService.selectTeacherStatisticsSum(teacherStatistics);
        if (tableDataInfoWithSumSaleMoney1.getTotalDealAmount() != null && tableDataInfoWithSumSaleMoney1.getTotalWithdrawnAmount().compareTo(BigDecimal.ZERO) > 0) {
            tableDataInfoWithSumSaleMoney1.setTotalServiceFeeRate(tableDataInfoWithSumSaleMoney1.getTotalServiceFee().divide(tableDataInfoWithSumSaleMoney1.getTotalWithdrawnAmount(), 2, RoundingMode.HALF_UP));
        } else {
            tableDataInfoWithSumSaleMoney1.setTotalServiceFeeRate(BigDecimal.ZERO);
        }
        //end
        TeacherStatistics teacherStatisticsEnd = new TeacherStatistics();
        teacherStatisticsEnd.setMobile("汇总数据:");
        teacherStatisticsEnd.setDealAmount(tableDataInfoWithSumSaleMoney1.getTotalDealAmount());
        teacherStatisticsEnd.setWithdrawnAmount(tableDataInfoWithSumSaleMoney1.getTotalWithdrawnAmount());
        teacherStatisticsEnd.setMoneyInTransit(tableDataInfoWithSumSaleMoney1.getTotalMoneyInTransit());
        teacherStatisticsEnd.setWithdrawableAmount(tableDataInfoWithSumSaleMoney1.getTotalWithdrawableAmount());
        teacherStatisticsEnd.setServiceFee(tableDataInfoWithSumSaleMoney1.getTotalServiceFee());
        teacherStatisticsEnd.setServiceFeeRate(tableDataInfoWithSumSaleMoney1.getTotalServiceFeeRate());
        list.add(teacherStatisticsEnd);
        ExcelUtil<TeacherStatistics> util = new ExcelUtil<>(TeacherStatistics.class);

        String fileName = "店铺资金数据" + UUID.randomUUID() + ".xlsx";

        //导出excel
        String extension = FilenameUtils.getExtension(fileName);
        String fileNameStr = UUID.randomUUID().toString() + "." + extension;
        //将文件存入本地临时文件再返回并保存到oss
        File newfile = util.exportExcelAndFile(list, "订单信息数据", fileNameStr);
        // 生成cos客户端
        COSClient cosClient = COSBuilder();
        String bucketName = outputBucket;
        String key = "export/" + fileNameStr;

        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, newfile);
        cosClient.putObject(putObjectRequest);
        newfile.delete();
        // 关闭 COS 客户端连接
        cosClient.shutdown();

        String returnUrl = "https://docpic-1319546384.cos.ap-nanjing.myqcloud.com/" + key;
        DerivedRecord derivedRecord = new DerivedRecord();
        derivedRecord.setOperatorId(teacherStatistics.getOperatorId());
        derivedRecord.setOperatorName(teacherStatistics.getOperatorName());
        derivedRecord.setOperatorPhone(teacherStatistics.getOperatorPhone());
        derivedRecord.setFileName(fileName);
        derivedRecord.setFilePath(returnUrl);
        derivedRecord.setCreateTime(DateUtils.getNowDate());
        derivedRecord.setUpdateTime(DateUtils.getNowDate());
        derivedRecordService.insertDerivedRecord(derivedRecord);
        return AjaxResult.success("导出成功");
    }

    @GetMapping("/withdrawRecordList")
    public TableDataInfoWithDrawRecord withdrawRecordList(TeacherStatistics teacherStatistics) {
        Long teacherId = SecurityUtils.getUserId();
        if (teacherId != 3L) {
            return TableDataInfoWithDrawRecord.error("没有权限");
        }
        if ("promoter".equals(teacherStatistics.getQueryType())) {
            //查询老师的推广员
            List<Long> promoterList = teacherStatisticsService.selectPromotersByTeacherId(teacherStatistics.getTeacherId());
            //查推广员提现记录:
            startPage();
            List<WithdrawRecord> list = CollectionUtils.isEmpty(promoterList) ? new ArrayList<>() : withdrawRecordService.selectWithdrawRecordListByPromoterIds(promoterList, teacherStatistics.getBeginTime(), teacherStatistics.getEndTime());
            TableDataInfoWithDrawRecord tableDataInfoWithDrawRecord = getTableDataInfoWithDrawRecord(list);
            if (!CollectionUtils.isEmpty(promoterList)) {
                teacherStatistics.setList(promoterList);
                TableDataInfoWithDrawRecord tableDataInfoWithDrawRecord1 = withdrawRecordService.sumWithdrawRecord(teacherStatistics);
                if (tableDataInfoWithDrawRecord1 != null) {
                    tableDataInfoWithDrawRecord.setTotalReceiveMoney(tableDataInfoWithDrawRecord1.getTotalReceiveMoney());
                    tableDataInfoWithDrawRecord.setTotalWithdrawnAmount(tableDataInfoWithDrawRecord1.getTotalWithdrawnAmount());
                    tableDataInfoWithDrawRecord.setTotalServiceFee(tableDataInfoWithDrawRecord1.getTotalServiceFee());
                }
            }
            return tableDataInfoWithDrawRecord;
        } else {
            //查推广员提现记录:
            startPage();
            List<WithdrawRecord> list = withdrawRecordService.selectWithdrawRecordListByTeacherId(teacherStatistics.getTeacherId(), teacherStatistics.getBeginTime(), teacherStatistics.getEndTime());
            TableDataInfoWithDrawRecord tableDataInfoWithDrawRecord = getTableDataInfoWithDrawRecord(list);
            TableDataInfoWithDrawRecord tableDataInfoWithDrawRecord1 = withdrawRecordService.sumWithdrawRecordForTeacher(teacherStatistics);
            if (tableDataInfoWithDrawRecord1 != null) {
                tableDataInfoWithDrawRecord.setTotalReceiveMoney(tableDataInfoWithDrawRecord1.getTotalReceiveMoney());
                tableDataInfoWithDrawRecord.setTotalWithdrawnAmount(tableDataInfoWithDrawRecord1.getTotalWithdrawnAmount());
                tableDataInfoWithDrawRecord.setTotalServiceFee(tableDataInfoWithDrawRecord1.getTotalServiceFee());
            }
            return tableDataInfoWithDrawRecord;
        }
    }

    public COSClient COSBuilder() {
        // 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(region));
        // 生成cos客户端
        return new COSClient(cred, clientConfig);
    }
} 