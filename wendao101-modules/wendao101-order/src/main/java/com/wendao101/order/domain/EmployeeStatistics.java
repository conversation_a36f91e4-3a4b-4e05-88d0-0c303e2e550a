package com.wendao101.order.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 教师统计对象 teacher_statistics
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeStatistics extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;
    /** 交易金额 */
    @Excel(name = "交易金额", scale = 2)
    private BigDecimal dealAmount;

    /** 已提现金额 */
    @Excel(name = "已提现金额", scale = 2)
    private BigDecimal withdrawnAmount;

    /** 在途资金 */
    @Excel(name = "在途资金", scale = 2)
    private BigDecimal moneyInTransit;

    /** 可提现金额 */
    @Excel(name = "可提现金额", scale = 2)
    private BigDecimal withdrawableAmount;

    /** 老师已提现金额收取的抽佣费(新) */
    private String timeQueryStr;
    /** 老师已提现金额收取的抽佣费*/
    private BigDecimal withdrawnAmountFee;
    /** 老师未提现金额将来要抽的费用 */
    private BigDecimal notWithdrawnFee;

    /**
     * 已提现金额收取的毛抽佣费+未提现金额将来要抽的毛抽佣费用
     */
    private BigDecimal totalWithdrawnFee;
    /** 员工id */
    private Long employeeId;
    /** 员工昵称姓名 **/
    private String nickName;


    /**
     * 已提现净抽佣
     */
    private BigDecimal withdrawnNetCommission;
    /**
     * 未提现净抽佣
     */
    private BigDecimal notWithdrawnNetCommission;

    /**
     * 已提现净抽佣+未提现净抽佣
     */
    private BigDecimal totalWithdrawnNetCommission;

} 