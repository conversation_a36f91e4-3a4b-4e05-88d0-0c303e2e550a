package com.wendao101.order.service.impl;

import java.util.Collections;
import java.util.List;

import com.wendao101.common.core.web.page.TableDataInfoWithEmployeeDataSum;
import com.wendao101.common.core.web.page.TableDataInfoWithSumSaleMoney;
import com.wendao101.order.domain.EmployeeStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.order.mapper.TeacherStatisticsMapper;
import com.wendao101.order.domain.TeacherStatistics;
import com.wendao101.order.service.ITeacherStatisticsService;

/**
 * 教师统计Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class TeacherStatisticsServiceImpl implements ITeacherStatisticsService {
    @Autowired
    private TeacherStatisticsMapper teacherStatisticsMapper;

    /**
     * 查询教师统计
     * 
     * @param id 教师统计主键
     * @return 教师统计
     */
    @Override
    public TeacherStatistics selectTeacherStatisticsById(Long id) {
        return teacherStatisticsMapper.selectTeacherStatisticsById(id);
    }

    /**
     * 查询教师统计列表
     * 
     * @param teacherStatistics 教师统计
     * @return 教师统计
     */
    @Override
    public List<TeacherStatistics> selectTeacherStatisticsList(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.selectTeacherStatisticsList(teacherStatistics);
    }

    /**
     * 新增教师统计
     * 
     * @param teacherStatistics 教师统计
     * @return 结果
     */
    @Override
    public int insertTeacherStatistics(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.insertTeacherStatistics(teacherStatistics);
    }

    /**
     * 修改教师统计
     * 
     * @param teacherStatistics 教师统计
     * @return 结果
     */
    @Override
    public int updateTeacherStatistics(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.updateTeacherStatistics(teacherStatistics);
    }

    /**
     * 批量删除教师统计
     * 
     * @param ids 需要删除的教师统计主键
     * @return 结果
     */
    @Override
    public int deleteTeacherStatisticsByIds(Long[] ids) {
        return teacherStatisticsMapper.deleteTeacherStatisticsByIds(ids);
    }

    /**
     * 删除教师统计信息
     * 
     * @param id 教师统计主键
     * @return 结果
     */
    @Override
    public int deleteTeacherStatisticsById(Long id) {
        return teacherStatisticsMapper.deleteTeacherStatisticsById(id);
    }

    @Override
    public TableDataInfoWithSumSaleMoney selectTeacherStatisticsSum(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.selectTeacherStatisticsSum(teacherStatistics);
    }

    @Override
    public List<Long> selectPromotersByTeacherId(Long teacherId) {
        return teacherStatisticsMapper.selectPromotersByTeacherId(teacherId);
    }

    @Override
    public List<EmployeeStatistics> selectEmployeeStatisticsList(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.selectEmployeeStatisticsList(teacherStatistics);
    }

    @Override
    public TableDataInfoWithEmployeeDataSum selectEmployeeStatisticsSum(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.selectEmployeeStatisticsSum(teacherStatistics);
    }
}